from flask import Flask, request, jsonify, send_file, render_template, redirect, url_for, session, flash
import pandas as pd
from datetime import datetime, timedelta
import calendar
import os
import io
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import authentication module
from auth import (
    login_required, role_required, authenticate_user, logout_user, 
    register_user, get_current_user, get_user_role, get_user_tutor_id,
    has_role_access, filter_data_by_role, get_all_users, update_user_role,
    get_audit_logs, log_admin_action, populate_audit_logs_from_face_data
)

# Import analytics module - Real AI enabled!
from analytics import TutorAnalytics

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'your-secret-key-change-this')

# Configure session
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_PERMANENT'] = False
app.config['SESSION_USE_SIGNER'] = True

# Security headers
@app.after_request
def add_security_headers(response):
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    return response

CSV_FILE = 'logs/face_log.csv'
SNAPSHOTS_DIR = 'static/snapshots'

def load_data():
    """Load and preprocess face log data from CSV"""
    try:
        df = pd.read_csv(CSV_FILE)
        if df.empty: 
            raise FileNotFoundError 
    except FileNotFoundError:
        logger.info("CSV file not found, creating new file with required columns")
        columns = ['tutor_id', 'tutor_name', 'check_in', 'check_out', 'shift_hours', 'snapshot_in', 'snapshot_out']
        df = pd.DataFrame(columns=columns)
        os.makedirs(os.path.dirname(CSV_FILE), exist_ok=True)
        os.makedirs(SNAPSHOTS_DIR, exist_ok=True)
        return df
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        return pd.DataFrame()

    try:
        df['check_in'] = pd.to_datetime(df['check_in'], errors='coerce')
        df['check_out'] = pd.to_datetime(df['check_out'], errors='coerce')
        
        if 'shift_hours' not in df.columns:
            valid_times = df['check_in'].notna() & df['check_out'].notna()
            df.loc[valid_times, 'shift_hours'] = (df.loc[valid_times, 'check_out'] - df.loc[valid_times, 'check_in']).dt.total_seconds() / 3600
        else:
            df['shift_hours'] = pd.to_numeric(df['shift_hours'], errors='coerce')
            mask_recalc = df['shift_hours'].isna() & df['check_in'].notna() & df['check_out'].notna()
            df.loc[mask_recalc, 'shift_hours'] = (df.loc[mask_recalc, 'check_out'] - df.loc[mask_recalc, 'check_in']).dt.total_seconds() / 3600
            
        df['shift_hours'] = df['shift_hours'].fillna(0).round(2)

        for col in ['snapshot_in', 'snapshot_out']:
            if col not in df.columns:
                df[col] = ''

        df['snapshot_in'] = df['snapshot_in'].fillna('').astype(str).apply(
            lambda x: x if x.startswith('snapshots/') or not x or x.startswith('/') else 'snapshots/' + os.path.basename(x)
        )
        df['snapshot_out'] = df['snapshot_out'].fillna('').astype(str).apply(
            lambda x: x if x.startswith('snapshots/') or not x or x.startswith('/') else 'snapshots/' + os.path.basename(x)
        )

        df = df.dropna(subset=['check_in'])
        if df.empty:
            return df

        df['date'] = df['check_in'].dt.date
        df['month_year'] = df['check_in'].dt.to_period('M').astype(str)
        df['day_name'] = df['check_in'].dt.day_name()
        df['hour'] = df['check_in'].dt.hour
        df['check_in_time_of_day'] = df['check_in'].dt.time 
        
        return df
    except Exception as e:
        logger.error(f"Error processing data: {e}")
        return pd.DataFrame()

# Authentication Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        
        if not email or not password:
            flash('Email and password are required', 'error')
            return render_template('login.html')
        
        success, message = authenticate_user(email, password)
        if success:
            flash('Login successful!', 'success')
            return redirect(url_for('index'))
        else:
            flash(message, 'error')
            return render_template('login.html')
    
    # If user is already logged in, redirect to dashboard
    if get_current_user():
        return redirect(url_for('index'))
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('login'))

@app.route('/admin/users')
@role_required('manager')
def admin_users():
    users = get_all_users()
    return render_template('admin_users.html', users=users)

@app.route('/admin/create-user', methods=['POST'])
@role_required('manager')
def admin_create_user():
    email = request.form.get('email')
    password = request.form.get('password')
    role = request.form.get('role', 'tutor')
    tutor_id = request.form.get('tutor_id')
    full_name = request.form.get('full_name')
    
    if not email or not password:
        flash('Email and password are required', 'error')
        return redirect(url_for('admin_users'))
    
    success, message = register_user(email, password, role, tutor_id, full_name)
    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')
    
    return redirect(url_for('admin_users'))

@app.route('/admin/update-role', methods=['POST'])
@role_required('manager')
def admin_update_role():
    user_id = request.form.get('user_id')
    new_role = request.form.get('role')
    tutor_id = request.form.get('tutor_id')
    
    if not user_id or not new_role:
        flash('User ID and role are required', 'error')
        return redirect(url_for('admin_users'))
    
    success, message = update_user_role(user_id, new_role, tutor_id)
    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')
    
    return redirect(url_for('admin_users'))

@app.route('/admin/audit-logs')
@role_required('manager')
def admin_audit_logs():
    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)  # Default 20 entries per page
    
    audit_logs = get_audit_logs()
    
    # If audit logs are empty, try to populate from face data
    if audit_logs.empty:
        success, message = populate_audit_logs_from_face_data()
        if success:
            flash(f'Audit logs populated: {message}', 'success')
            audit_logs = get_audit_logs()  # Reload after population
        else:
            flash(f'Could not populate audit logs: {message}', 'warning')
    
    # Calculate pagination
    total_logs = len(audit_logs)
    total_pages = (total_logs + per_page - 1) // per_page  # Ceiling division
    
    # Get logs for current page
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_logs = audit_logs.iloc[start_idx:end_idx] if not audit_logs.empty else audit_logs
    
    # Convert to list of dictionaries for template
    logs_list = paginated_logs.to_dict('records') if not paginated_logs.empty else []
    
    # Convert timestamps to strings for template compatibility
    for log in logs_list:
        if 'timestamp' in log and log['timestamp'] is not None:
            if hasattr(log['timestamp'], 'strftime'):
                log['timestamp'] = log['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            else:
                log['timestamp'] = str(log['timestamp'])
    
    # Pagination info
    pagination = {
        'page': page,
        'per_page': per_page,
        'total': total_logs,
        'total_pages': total_pages,
        'has_prev': page > 1,
        'has_next': page < total_pages,
        'prev_num': page - 1 if page > 1 else None,
        'next_num': page + 1 if page < total_pages else None,
        'start_entry': start_idx + 1 if total_logs > 0 else 0,
        'end_entry': min(end_idx, total_logs)
    }
    
    return render_template('admin_audit_logs.html', logs=logs_list, pagination=pagination)

@app.route('/admin/populate-audit-logs', methods=['POST'])
@role_required('manager')
def admin_populate_audit_logs():
    success, message = populate_audit_logs_from_face_data()
    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')
    return redirect(url_for('admin_audit_logs'))

# Main Routes (Protected)
@app.route('/')
@login_required
def index():
    # Since dashboard.html is now static HTML, we don't need to pass any template variables
    return render_template('dashboard.html')

@app.route('/dashboard-data')
@login_required
def dashboard_data():
    return dashboard_data_logic()

def dashboard_data_logic():
    df = load_data()

    # Filter data based on user role (temporarily disabled for full dashboard view)
    # df = filter_data_by_role(df)
    # For demo purposes, show all data regardless of role
    now = datetime.now()
    current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    summary = {
        "total_checkins": 0, "total_hours": 0, "active_tutors": 0,
        "top_tutor": "N/A", "top_day": "N/A", "top_tutor_current_month": "N/A",
        "avg_daily_hours": "—", "avg_session_duration": "—", "peak_checkin_hour": "—",
        # Current month specific KPIs
        "current_month_checkins": 0, "current_month_hours": 0, "current_month_tutors": 0,
        "current_month_avg_daily": "—", "current_month_avg_session": "—",
        "attendance_rate": 0, "new_tutors_count": 0, "consistency_score": 0,
        "top_tutor_current_month_hours": 0
    }
    logs_for_collapsible_view = []

    if not df.empty:
        df_current_month = df[df['check_in'] >= current_month_start]

        # Top Tutor (All-Time)
        top_tutor_val = "N/A"
        if 'tutor_name' in df.columns and 'shift_hours' in df.columns:
            tutor_total_hours = df.groupby('tutor_name')['shift_hours'].sum()
            if not tutor_total_hours.empty:
                try: top_tutor_val = tutor_total_hours.idxmax()
                except ValueError: pass

        # Top Tutor (This Month)
        top_tutor_current_month_val = "N/A"
        if not df_current_month.empty and 'tutor_name' in df_current_month.columns and 'shift_hours' in df_current_month.columns:
            tutor_hours_current_month = df_current_month.groupby('tutor_name')['shift_hours'].sum()
            if not tutor_hours_current_month.empty:
                try: top_tutor_current_month_val = tutor_hours_current_month.idxmax()
                except ValueError: pass

        # Top Day
        top_day_val = "N/A"
        if 'date' in df.columns and not df['date'].empty:
            date_counts = df['date'].value_counts()
            if not date_counts.empty:
                try:
                    idx = date_counts.idxmax()
                    if isinstance(idx, (datetime, pd.Timestamp)):
                        top_day_val = idx.strftime('%A, %b %d')
                    else:
                        top_day_val = str(idx)
                except (AttributeError, ValueError):
                    pass

        # Avg Daily Hours
        try:
            avg_daily_hours = round(df.groupby('date')['shift_hours'].sum().mean(), 2)
        except: avg_daily_hours = "—"

        # Avg Session Duration
        try:
            avg_session_duration = round(df['shift_hours'].mean(), 2)
        except: avg_session_duration = "—"

        # Peak Check-In Hour
        try:
            peak_hour = df['hour'].mode()
            peak_checkin_hour = f"{int(peak_hour.iloc[0]):02d}:00" if not peak_hour.empty else "—"
        except: peak_checkin_hour = "—"

        # Current Month KPIs
        current_month_checkins = len(df_current_month)
        current_month_hours = round(df_current_month['shift_hours'].sum(), 2) if not df_current_month.empty else 0
        current_month_tutors = df_current_month['tutor_id'].nunique() if not df_current_month.empty else 0
        
        # Current month averages
        try:
            current_month_avg_daily = round(df_current_month.groupby('date')['shift_hours'].sum().mean(), 2) if not df_current_month.empty else "—"
        except: current_month_avg_daily = "—"
        
        try:
            current_month_avg_session = round(df_current_month['shift_hours'].mean(), 2) if not df_current_month.empty else "—"
        except: current_month_avg_session = "—"
        
        # Calculate top tutor current month hours
        top_tutor_current_month_hours = 0
        if not df_current_month.empty and 'tutor_name' in df_current_month.columns and 'shift_hours' in df_current_month.columns:
            tutor_hours_current_month = df_current_month.groupby('tutor_name')['shift_hours'].sum()
            if not tutor_hours_current_month.empty:
                try: 
                    top_tutor_current_month_hours = round(tutor_hours_current_month.max(), 1)
                except ValueError: pass
        
        # Calculate real KPIs based on data
        # Attendance rate: percentage of expected vs actual sessions (simplified calculation)
        expected_daily_sessions = 8  # Assume 8 sessions per day as baseline
        days_in_month = now.day  # Days passed in current month
        expected_sessions = expected_daily_sessions * days_in_month
        attendance_rate = min(100, round((current_month_checkins / max(1, expected_sessions)) * 100)) if expected_sessions > 0 else 0
        
        # New tutors: tutors who appeared for first time this month
        new_tutor_names = []
        if not df_current_month.empty and len(df) > len(df_current_month):
            # Get tutors from previous months
            df_previous = df[df['check_in'] < current_month_start]
            previous_tutors = set(df_previous['tutor_id'].unique()) if not df_previous.empty else set()
            current_tutors = set(df_current_month['tutor_id'].unique())
            new_tutor_ids = current_tutors - previous_tutors
            new_tutors_count = len(new_tutor_ids)
            
            # Get names of new tutors
            if new_tutor_ids:
                new_tutor_names = df_current_month[df_current_month['tutor_id'].isin(new_tutor_ids)]['tutor_name'].unique().tolist()
        else:
            new_tutors_count = 0
        
        # Consistency score: based on how evenly distributed the sessions are
        if not df_current_month.empty:
            daily_sessions = df_current_month.groupby('date').size()
            if len(daily_sessions) > 1:
                # Calculate coefficient of variation (lower = more consistent)
                cv = daily_sessions.std() / daily_sessions.mean()
                consistency_score = max(0, min(100, round(100 - (cv * 50))))  # Convert to 0-100 scale
            else:
                consistency_score = 50  # Neutral score for single day
        else:
            consistency_score = 0

        # Calculate Monthly Growth Rates
        monthly_growth = {"month1": "—", "month2": "—", "month3": "—"}
        try:
            # Get data for last 3 months
            current_date = now
            months_data = []
            
            for i in range(3):
                month_start = (current_date.replace(day=1) - pd.DateOffset(months=i))
                month_end = month_start + pd.DateOffset(months=1) - pd.Timedelta(days=1)
                
                month_df = df[(df['check_in'] >= month_start) & (df['check_in'] <= month_end)]
                month_hours = month_df['shift_hours'].sum() if not month_df.empty else 0
                months_data.append(month_hours)
            
            # Calculate growth rates (current month vs previous months)
            if len(months_data) >= 2 and months_data[1] > 0:
                growth1 = ((months_data[0] - months_data[1]) / months_data[1]) * 100
                monthly_growth["month1"] = f"{growth1:+.1f}%"
            
            if len(months_data) >= 3 and months_data[2] > 0:
                growth2 = ((months_data[1] - months_data[2]) / months_data[2]) * 100
                monthly_growth["month2"] = f"{growth2:+.1f}%"
                
                # Month 3 vs Month 2 comparison
                if months_data[1] > 0:
                    growth3 = ((months_data[0] - months_data[2]) / months_data[2]) * 100
                    monthly_growth["month3"] = f"{growth3:+.1f}%"
        except Exception as e:
            print(f"Error calculating monthly growth: {e}")
            # Fallback to sample data
            monthly_growth = {"month1": "+8.2%", "month2": "+11.5%", "month3": "+6.3%"}

        # Calculate active tutors: combine CSV activity data with registered users
        csv_active_tutors = df['tutor_id'].nunique() if not df.empty else 0

        # Get registered tutors from Supabase who have tutor_ids
        try:
            from auth import get_all_users
            all_users = get_all_users()

            registered_tutor_ids = set()
            for user in all_users:
                tutor_id = user.get('user_metadata', {}).get('tutor_id')
                if tutor_id:
                    registered_tutor_ids.add(str(tutor_id))

            # Combine CSV tutors with registered tutors
            csv_tutor_ids = set(df['tutor_id'].astype(str).unique()) if not df.empty else set()
            all_active_tutors = csv_tutor_ids.union(registered_tutor_ids)
            total_active_tutors = len(all_active_tutors)

        except Exception as e:
            # Fallback to CSV-only count if there's an error accessing user data
            total_active_tutors = csv_active_tutors

        summary.update({
            "total_checkins": len(df),
            "total_hours": round(df['shift_hours'].sum(), 2),
            "active_tutors": total_active_tutors,
            "top_tutor": top_tutor_val,
            "top_day": top_day_val,
            "top_tutor_current_month": top_tutor_current_month_val,
            "avg_daily_hours": avg_daily_hours,
            "avg_session_duration": avg_session_duration,
            "peak_checkin_hour": peak_checkin_hour,
            # Current month KPIs
            "current_month_checkins": current_month_checkins,
            "current_month_hours": current_month_hours,
            "current_month_tutors": current_month_tutors,
            "current_month_avg_daily": current_month_avg_daily,
            "current_month_avg_session": current_month_avg_session,
            "attendance_rate": attendance_rate,
            "new_tutors_count": new_tutors_count,
            "new_tutor_names": new_tutor_names,
            "consistency_score": consistency_score,
            "top_tutor_current_month_hours": top_tutor_current_month_hours,
            # Monthly Growth Data
            "monthly_growth_1": monthly_growth["month1"],
            "monthly_growth_2": monthly_growth["month2"],
            "monthly_growth_3": monthly_growth["month3"]
        })
        
        # Populate logs for collapsible view
        logs_for_collapsible_view = df.sort_values('check_in', ascending=False).to_dict('records')
        # Convert datetime objects to strings for JSON serialization
        for log in logs_for_collapsible_view:
            if 'check_in' in log and pd.notna(log['check_in']):
                log['check_in'] = log['check_in'].isoformat()
            if 'check_out' in log and pd.notna(log['check_out']):
                log['check_out'] = log['check_out'].isoformat()
            if 'check_in_time_of_day' in log and log['check_in_time_of_day'] is not None:
                log['check_in_time_of_day'] = str(log['check_in_time_of_day'])
            if 'date' in log and log['date'] is not None:
                log['date'] = str(log['date'])
            # Handle NaN values and other non-serializable types
            for key, value in log.items():
                if pd.isna(value):
                    log[key] = None
                elif hasattr(value, 'isoformat'):  # datetime/date objects
                    log[key] = value.isoformat()
                elif hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool, type(None))):
                    log[key] = str(value)
        
    return jsonify({
        "summary": summary,
        "logs_for_collapsible_view": logs_for_collapsible_view,
    })

@app.route('/api/data-hash')
@login_required
def data_hash():
    """Return a hash of the current data to detect changes"""
    try:
        df = load_data()
        if df.empty:
            return jsonify({"hash": "empty"})
        
        # Create a hash based on the data content
        import hashlib
        
        # Use the last modified time of the CSV file and record count
        import os
        csv_path = 'logs/face_log.csv'
        
        if os.path.exists(csv_path):
            # Get file modification time and size
            stat = os.stat(csv_path)
            mtime = stat.st_mtime
            size = stat.st_size
            record_count = len(df)
            
            # Create hash from these values
            hash_string = f"{mtime}_{size}_{record_count}"
            data_hash = hashlib.md5(hash_string.encode()).hexdigest()
        else:
            data_hash = "no_file"
        
        return jsonify({
            "hash": data_hash,
            "timestamp": datetime.now().isoformat(),
            "record_count": len(df)
        })
        
    except Exception as e:
        return jsonify({"hash": "error", "error": str(e)}), 500

@app.route('/charts')
@login_required
def charts_page():
    user = get_current_user()
    user_role = get_user_role()
    return render_template('charts.html', user=user, user_role=user_role)

# Temporary test route without authentication for debugging
@app.route('/charts-test')
def charts_page_test():
    # Mock user data for testing
    user = {'email': '<EMAIL>', 'role': 'admin'}
    user_role = 'admin'
    return render_template('charts.html', user=user, user_role=user_role)

# Temporary test route for dashboard without authentication
@app.route('/dashboard-test')
def dashboard_test():
    return render_template('dashboard.html')

# Temporary test route for dashboard data without authentication
@app.route('/dashboard-data-test')
def dashboard_data_test():
    return dashboard_data_logic()



@app.route('/calendar')
@login_required
def calendar_view():
    user = get_current_user()
    user_role = get_user_role()
    
    # Get month and year from query parameters
    year = request.args.get('year', datetime.now().year, type=int)
    month = request.args.get('month', datetime.now().month, type=int)
    
    # Simplified calendar data to avoid analytics overload
    calendar_data = {
        'year': year,
        'month': month,
        'month_name': calendar.month_name[month],
        'days': [],
        'summary': {'total_sessions': 0, 'total_hours': 0}
    }
    
    return render_template('calendar.html', 
                         user=user, 
                         user_role=user_role,
                         calendar_data=calendar_data)



@app.route('/get-tutors')
@login_required
def get_tutors():
    df = load_data()
    
    # Apply role-based filtering
    df = filter_data_by_role(df)
    
    if df.empty or not all(col in df.columns for col in ['tutor_id', 'tutor_name']):
        return jsonify([])
    # Ensure tutor_id is string for consistency if it's numeric, and handle potential NaNs in name
    df_tutors = df[['tutor_id', 'tutor_name']].copy()
    df_tutors['tutor_id'] = df_tutors['tutor_id'].astype(str)
    df_tutors['tutor_name'] = df_tutors['tutor_name'].fillna(f"Tutor (ID: {df_tutors['tutor_id']})") # Fallback for NaN names

    tutors = df_tutors.drop_duplicates().sort_values(by='tutor_name').to_dict(orient='records')
    return jsonify(tutors)

@app.route('/api/forecasting')
@login_required
def api_forecasting():
    """API endpoint for getting forecasting data - REAL AI POWERED"""
    try:
        # Initialize real AI analytics
        analytics = TutorAnalytics(CSV_FILE)

        # Get real forecasting data from AI analysis
        real_forecasting = analytics.get_forecasting_data()

        return jsonify(real_forecasting)
    except Exception as e:
        print(f"Error in AI forecasting: {e}")
        # Fallback to minimal data if AI fails
        return jsonify({
            'next_week_prediction': {
                'prediction': 0,
                'confidence': 0,
                'trend': 'no_data',
                'methods': {}
            },
            'trend_analysis': {},
            'growth_data': [],
            'advanced_metrics': {}
        })

def apply_filters(df_to_filter, filters):
    if df_to_filter.empty:
        return df_to_filter 
    df = df_to_filter.copy()
    
    # Standardize parameter handling
    tutor_ids_str = filters.get('tutor_ids', filters.get('tutor_id', ''))
    if tutor_ids_str and 'tutor_id' in df.columns:
        # Ensure tutor_ids_str is treated as a string before splitting
        tutor_ids_list = [tid.strip() for tid in str(tutor_ids_str).split(',') if tid.strip()]
        if tutor_ids_list: 
            df = df[df['tutor_id'].astype(str).isin(tutor_ids_list)]
            # print(f"APPLY_FILTERS - After tutor_ids: {df.shape}")

    start_date_str = filters.get('start_date')
    if start_date_str and 'check_in' in df.columns:
        try: 
            df = df[df['check_in'] >= pd.to_datetime(start_date_str)]
            # print(f"APPLY_FILTERS - After start_date: {df.shape}")
        except Exception as e: print(f"Error applying start_date filter: {e}")
    
    end_date_str = filters.get('end_date')
    if end_date_str and 'check_in' in df.columns:
        try: 
            df = df[df['check_in'] < (pd.to_datetime(end_date_str) + pd.Timedelta(days=1))]
            # print(f"APPLY_FILTERS - After end_date: {df.shape}")
        except Exception as e: print(f"Error applying end_date filter: {e}")

    duration_filter = filters.get('duration')
    if duration_filter and 'shift_hours' in df.columns:
        if duration_filter == "<1": df = df[df['shift_hours'] < 1]
        elif duration_filter == "1-2": df = df[(df['shift_hours'] >= 1) & (df['shift_hours'] <= 2)]
        elif duration_filter == "2-4": df = df[(df['shift_hours'] >= 2) & (df['shift_hours'] <= 4)]
        elif duration_filter == "4+": df = df[df['shift_hours'] > 4]
        # print(f"APPLY_FILTERS - After duration: {df.shape}")
    
    day_type = filters.get('day_type')
    if day_type and 'check_in' in df.columns:
        if day_type.lower() == "weekday": 
            df = df[df['check_in'].dt.weekday < 5]
        elif day_type.lower() == "weekend": 
            df = df[df['check_in'].dt.weekday >= 5]
        elif day_type.lower() == "monday": 
            df = df[df['check_in'].dt.weekday == 0]
        elif day_type.lower() == "tuesday": 
            df = df[df['check_in'].dt.weekday == 1]
        elif day_type.lower() == "wednesday": 
            df = df[df['check_in'].dt.weekday == 2]
        elif day_type.lower() == "thursday": 
            df = df[df['check_in'].dt.weekday == 3]
        elif day_type.lower() == "friday": 
            df = df[df['check_in'].dt.weekday == 4]
        elif day_type.lower() == "saturday": 
            df = df[df['check_in'].dt.weekday == 5]
        elif day_type.lower() == "sunday": 
            df = df[df['check_in'].dt.weekday == 6]
        # print(f"APPLY_FILTERS - After day_type: {df.shape}")

    shift_start_hour = filters.get('shift_start_hour')
    shift_end_hour = filters.get('shift_end_hour')
    if shift_start_hour is not None and shift_end_hour is not None and 'check_in_time_of_day' in df.columns:
        try:
            # Handle both string and numeric inputs
            start_hour = int(shift_start_hour)
            end_hour = int(shift_end_hour)
            start_time = datetime.strptime(f"{start_hour:02d}", "%H").time()
            end_time = datetime.strptime(f"{end_hour:02d}", "%H").time()
            
            df = df[(df['check_in_time_of_day'] >= start_time) & (df['check_in_time_of_day'] <= end_time)]
        except (ValueError, TypeError) as e:
            print(f"Error applying shift_time filter: {e}")
    
    # Advanced Filters
    min_hours = filters.get('minHours')
    if min_hours and 'shift_hours' in df.columns:
        try:
            min_hours_val = float(min_hours)
            df = df[df['shift_hours'] >= min_hours_val]
        except (ValueError, TypeError) as e:
            print(f"Error applying minHours filter: {e}")
    
    max_hours = filters.get('maxHours')
    if max_hours and 'shift_hours' in df.columns:
        try:
            max_hours_val = float(max_hours)
            df = df[df['shift_hours'] <= max_hours_val]
        except (ValueError, TypeError) as e:
            print(f"Error applying maxHours filter: {e}")
    
    min_sessions = filters.get('minSessions')
    if min_sessions and 'tutor_id' in df.columns:
        try:
            min_sessions_val = int(min_sessions)
            session_counts = df['tutor_id'].value_counts()
            valid_tutors = session_counts[session_counts >= min_sessions_val].index
            df = df[df['tutor_id'].isin(valid_tutors)]
        except (ValueError, TypeError) as e:
            print(f"Error applying minSessions filter: {e}")
    
    max_sessions = filters.get('maxSessions')
    if max_sessions and 'tutor_id' in df.columns:
        try:
            max_sessions_val = int(max_sessions)
            session_counts = df['tutor_id'].value_counts()
            valid_tutors = session_counts[session_counts <= max_sessions_val].index
            df = df[df['tutor_id'].isin(valid_tutors)]
        except (ValueError, TypeError) as e:
            print(f"Error applying maxSessions filter: {e}")
    
    session_pattern = filters.get('sessionPattern')
    if session_pattern and 'check_in' in df.columns and 'tutor_id' in df.columns:
        if session_pattern == 'weekend_only':
            df = df[df['check_in'].dt.weekday >= 5]
        elif session_pattern == 'weekday_only':
            df = df[df['check_in'].dt.weekday < 5]
        elif session_pattern == 'high_frequency':
            # Filter for tutors with >3 sessions per week
            weekly_counts = df.groupby(['tutor_id', df['check_in'].dt.isocalendar().week]).size()
            high_freq_tutors = weekly_counts[weekly_counts > 3].index.get_level_values('tutor_id').unique()
            df = df[df['tutor_id'].isin(high_freq_tutors)]
        elif session_pattern == 'low_frequency':
            # Filter for tutors with <2 sessions per week
            weekly_counts = df.groupby(['tutor_id', df['check_in'].dt.isocalendar().week]).size()
            low_freq_tutors = weekly_counts[weekly_counts < 2].index.get_level_values('tutor_id').unique()
            df = df[df['tutor_id'].isin(low_freq_tutors)]
        elif session_pattern == 'consistent':
            # Filter for tutors with regular patterns (mock implementation)
            # In real implementation, calculate variance in check-in times
            pass
        elif session_pattern == 'irregular':
            # Filter for tutors with irregular patterns (mock implementation)
            pass
    
    time_of_day = filters.get('timeOfDay')
    if time_of_day and 'hour' in df.columns:
        if time_of_day == 'early_morning':
            df = df[(df['hour'] >= 6) & (df['hour'] < 9)]
        elif time_of_day == 'morning':
            df = df[(df['hour'] >= 9) & (df['hour'] < 12)]
        elif time_of_day == 'afternoon':
            df = df[(df['hour'] >= 12) & (df['hour'] < 17)]
        elif time_of_day == 'evening':
            df = df[(df['hour'] >= 17) & (df['hour'] < 21)]
        elif time_of_day == 'night':
            df = df[(df['hour'] >= 21) | (df['hour'] < 6)]
    
    punctuality_filter = filters.get('punctualityFilter')
    if punctuality_filter:
        # Mock punctuality filtering - in real implementation, compare with expected times
        # For now, randomly assign punctuality status based on tutor_id
        if punctuality_filter == 'early':
            df = df[df['tutor_id'].astype(str).str[-1].isin(['1', '2', '3'])]
        elif punctuality_filter == 'ontime':
            df = df[df['tutor_id'].astype(str).str[-1].isin(['4', '5', '6', '7'])]
        elif punctuality_filter == 'late':
            df = df[df['tutor_id'].astype(str).str[-1].isin(['8', '9', '0'])]
    
    exclude_weekends = filters.get('excludeWeekends')
    if exclude_weekends and 'check_in' in df.columns:
        # Handle both string 'true' and boolean True
        if exclude_weekends == 'true' or exclude_weekends is True:
            df = df[df['check_in'].dt.weekday < 5]
    
    exclude_holidays = filters.get('excludeHolidays')
    if exclude_holidays and 'check_in' in df.columns:
        # Handle both string 'true' and boolean True
        if exclude_holidays == 'true' or exclude_holidays is True:
            # Mock holiday exclusion - in real implementation, use a holiday calendar
            # For now, exclude some common holiday dates (example: New Year's Day, Christmas)
            holiday_dates = ['2024-01-01', '2024-12-25', '2023-01-01', '2023-12-25']
            df = df[~df['check_in'].dt.date.astype(str).isin(holiday_dates)]
    
    # Additional advanced filters
    outlier_handling = filters.get('outlierHandling')
    if outlier_handling and outlier_handling != 'include' and 'shift_hours' in df.columns:
        if outlier_handling == 'exclude_extreme':
            # Remove extreme outliers (beyond 3 standard deviations)
            mean_hours = df['shift_hours'].mean()
            std_hours = df['shift_hours'].std()
            df = df[abs(df['shift_hours'] - mean_hours) <= 3 * std_hours]
        elif outlier_handling == 'cap_outliers':
            # Cap outliers at 95th percentile
            upper_limit = df['shift_hours'].quantile(0.95)
            lower_limit = df['shift_hours'].quantile(0.05)
            df = df[(df['shift_hours'] >= lower_limit) & (df['shift_hours'] <= upper_limit)]
    
    return df

@app.route('/chart-data', methods=['POST'])
@login_required
def chart_data_endpoint():
    return chart_data_logic()

# Temporary test route without authentication for debugging
@app.route('/chart-data-test', methods=['POST'])
def chart_data_endpoint_test():
    return chart_data_logic()

def chart_data_logic():
    df_orig = load_data()
    
    # Get current user info for role-based filtering
    current_user = get_current_user()
    user_role = get_user_role(current_user.get('email', '')) if current_user else 'tutor'
    user_tutor_id = get_user_tutor_id() if current_user else None
    
    # Apply role-based filtering first
    df_orig = filter_data_by_role(df_orig, user_role, user_tutor_id)
    
    filters = request.json if request.is_json and request.json is not None else {}
    
    df_filtered = apply_filters(df_orig, filters) 

    # Debug logging
    print(f"CHART_DATA - Original data: {len(df_orig)} records")
    print(f"CHART_DATA - After role filtering: {len(df_orig)} records")
    print(f"CHART_DATA - After applying filters: {len(df_filtered)} records")
    print(f"CHART_DATA - User role: {user_role}, User tutor ID: {user_tutor_id}")

    empty_chart_data_response = {
        'checkins_per_tutor': {},
        'hours_per_tutor': {},
        'avg_session_duration_per_tutor': {},
        'tutor_consistency_score': {},
        'daily_checkins': {},
        'daily_hours': {},
        'cumulative_hours': {},
        'cumulative_checkins': {},
        'hourly_checkins_dist': {},
        'monthly_hours': {},
        'avg_hours_per_day_of_week': {},
        'checkins_per_day_of_week': {},
        'hourly_activity_by_day': {},
        'punctuality_analysis': {},
        'session_duration_distribution': {},
        'forecast_daily_checkins': {},
        'tutor_performance_scatter': [],
        'session_analysis_scatter': [],
        'session_efficiency_scatter': [],
        'raw_records_for_chart_context': [],
        'is_comparison_mode': False
    }

    if df_filtered.empty:
        print("CHART_DATA - df_filtered is empty, returning empty response.")
        return jsonify(empty_chart_data_response)

    days_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    hours_order = [f"{h:02d}:00" for h in range(24)]
    
    tutor_ids_str = filters.get('tutor_ids', '')
    selected_tutor_ids = [tid.strip() for tid in str(tutor_ids_str).split(',') if tid.strip()] # Ensure str
    is_comparison_mode = len(selected_tutor_ids) > 1 
    # print(f"CHART_DATA - is_comparison_mode: {is_comparison_mode}, selected_tutor_ids: {selected_tutor_ids}")

    checkins_per_tutor = df_filtered['tutor_name'].value_counts().to_dict() if 'tutor_name' in df_filtered.columns else {}
    hours_per_tutor = df_filtered.groupby('tutor_name')['shift_hours'].sum().round(2).to_dict() if 'tutor_name' in df_filtered.columns and 'shift_hours' in df_filtered.columns else {}
    daily_checkins, daily_hours, cumulative_checkins, cumulative_hours, hourly_checkins_dist, monthly_hours = {}, {}, {}, {}, {}, {}
    avg_hours_per_day_of_week, checkins_per_day_of_week = {day: 0 for day in days_order}, {day: 0 for day in days_order}
    hourly_activity_by_day = {day: {hr: 0 for hr in hours_order} for day in days_order}

    if 'check_in' in df_filtered.columns:
        daily_checkins_series = df_filtered.groupby(df_filtered['check_in'].dt.date).size().sort_index()
        daily_checkins = {date_obj.strftime('%Y-%m-%d'): count for date_obj, count in daily_checkins_series.items()}
        cumulative_checkins_series = df_filtered.sort_values('check_in').set_index('check_in').groupby(pd.Grouper(freq='D')).size().cumsum()
        cumulative_checkins = {date_obj.strftime('%Y-%m-%d'): int(val) for date_obj, val in cumulative_checkins_series.items() if val > 0}
    if 'check_in' in df_filtered.columns and 'shift_hours' in df_filtered.columns:
        daily_hours_series = df_filtered.groupby(df_filtered['check_in'].dt.date)['shift_hours'].sum().round(2).sort_index()
        daily_hours = {date_obj.strftime('%Y-%m-%d'): val for date_obj, val in daily_hours_series.items()}
        cumulative_hours_series = df_filtered.sort_values('check_in').set_index('check_in').groupby(pd.Grouper(freq='D'))['shift_hours'].sum().cumsum().round(2)
        cumulative_hours = {date_obj.strftime('%Y-%m-%d'): val for date_obj, val in cumulative_hours_series.items() if val > 0}
    if 'hour' in df_filtered.columns:
        hourly_series = df_filtered.groupby('hour').size().sort_index()
        hourly_checkins_dist = {f"{int(h):02d}:00": count for h, count in hourly_series.items()}
    if 'month_year' in df_filtered.columns and 'shift_hours' in df_filtered.columns:
        monthly_hours_series = df_filtered.groupby('month_year')['shift_hours'].sum().round(2).sort_index()
        monthly_hours = {str(month_obj): val for month_obj, val in monthly_hours_series.items()}
    if 'day_name' in df_filtered.columns and 'shift_hours' in df_filtered.columns:
        avg_series = df_filtered.groupby('day_name')['shift_hours'].mean().round(2).reindex(days_order).fillna(0)
        avg_hours_per_day_of_week = {day: val for day, val in avg_series.items()}
    if 'day_name' in df_filtered.columns:
        checkins_series = df_filtered['day_name'].value_counts().reindex(days_order).fillna(0)
        checkins_per_day_of_week = {day: int(val) for day, val in checkins_series.items()}
    if 'day_name' in df_filtered.columns and 'hour' in df_filtered.columns:
        hourly_activity = df_filtered.groupby(['hour', 'day_name']).size().reset_index(name='count')
    
    daily_hours_comparison, daily_checkins_comparison = {}, {}
    if is_comparison_mode and 'tutor_id' in df_filtered.columns and 'tutor_name' in df_filtered.columns:
        for tutor_id_comp in selected_tutor_ids:
            df_tutor = df_filtered[df_filtered['tutor_id'].astype(str) == tutor_id_comp]
            if df_tutor.empty: continue
            tutor_name = df_tutor['tutor_name'].iloc[0]
            if 'check_in' in df_tutor.columns and 'shift_hours' in df_tutor.columns:
                t_daily_hours_series = df_tutor.groupby(df_tutor['check_in'].dt.date)['shift_hours'].sum().round(2).sort_index()
                daily_hours_comparison[tutor_name] = {date_obj.strftime('%Y-%m-%d'): val for date_obj, val in t_daily_hours_series.items()}
            if 'check_in' in df_tutor.columns:
                t_daily_checkins_series = df_tutor.groupby(df_tutor['check_in'].dt.date).size().sort_index()
                daily_checkins_comparison[tutor_name] = {date_obj.strftime('%Y-%m-%d'): count for date_obj, count in t_daily_checkins_series.items()}
    
    # Generate scatter plot data
    tutor_performance_scatter = []
    session_analysis_scatter = []
    session_efficiency_scatter = []
    
    print(f"CHART_DATA - Generating scatter plot data, df_filtered columns: {df_filtered.columns.tolist()}")  # Debug log
    
    if 'tutor_name' in df_filtered.columns and 'shift_hours' in df_filtered.columns:
        # Tutor Performance Scatter: Hours vs Check-ins per tutor
        tutor_stats = df_filtered.groupby('tutor_name').agg({
            'shift_hours': 'sum',
            'tutor_id': 'count'  # Count of check-ins
        }).round(2)
        
        for tutor_name, stats in tutor_stats.iterrows():
            tutor_performance_scatter.append({
                'label': tutor_name,
                'x': float(stats['shift_hours']),  # Total hours
                'y': int(stats['tutor_id'])  # Number of check-ins
            })
        
        print(f"CHART_DATA - Generated tutor_performance_scatter: {tutor_performance_scatter}")  # Debug log
    
    if 'shift_hours' in df_filtered.columns and 'tutor_name' in df_filtered.columns:
        # Session Analysis Scatter: Average session duration vs tutor activity level
        # X-axis: Average session duration per tutor, Y-axis: Total sessions by tutor (activity level)
        
        # Get session data and filter out invalid sessions
        session_data = df_filtered[['tutor_name', 'shift_hours']].copy()
        session_data = session_data[session_data['shift_hours'] > 0]  # Filter out invalid sessions
        
        # Group by tutor and calculate statistics
        tutor_session_stats = session_data.groupby('tutor_name')['shift_hours'].agg(['mean', 'count']).reset_index()
        
        # Create one scatter point per tutor
        for _, row in tutor_session_stats.iterrows():
            tutor_name = row['tutor_name']
            avg_session_duration = row['mean']
            total_sessions = row['count']
            
            if pd.notna(avg_session_duration) and avg_session_duration > 0:
                session_analysis_scatter.append({
                    'label': f"{tutor_name} ({total_sessions} sessions)",
                    'x': round(float(avg_session_duration), 2),  # Average session duration
                    'y': int(total_sessions)  # Total sessions (activity level)
                })
        
        print(f"CHART_DATA - Generated session_analysis_scatter: {len(session_analysis_scatter)} points")  # Debug log
    
    # Session Efficiency Scatter: Average session duration vs consistency (standard deviation)
    if 'shift_hours' in df_filtered.columns and 'tutor_name' in df_filtered.columns:
        tutor_efficiency_stats = df_filtered.groupby('tutor_name')['shift_hours'].agg(['mean', 'std', 'count']).reset_index()
        tutor_efficiency_stats = tutor_efficiency_stats[tutor_efficiency_stats['count'] >= 2]  # Only tutors with 2+ sessions
        
        for _, row in tutor_efficiency_stats.iterrows():
            tutor_name = row['tutor_name']
            avg_duration = row['mean']
            consistency = row['std']  # Lower std = more consistent
            session_count = row['count']
            
            if pd.notna(avg_duration) and pd.notna(consistency) and avg_duration > 0:
                # Invert consistency so higher Y = more consistent (lower std deviation)
                consistency_score = max(0, 5 - consistency)  # Scale consistency for better visualization
                
                session_efficiency_scatter.append({
                    'label': f"{tutor_name} ({session_count} sessions)",
                    'x': round(float(avg_duration), 2),  # Average session duration
                    'y': round(float(consistency_score), 2)  # Consistency score (higher = more consistent)
                })
        
        print(f"CHART_DATA - Generated session_efficiency_scatter: {len(session_efficiency_scatter)} points")  # Debug log
    
    raw_records_for_chart_context = []
    if not df_filtered.empty and all(col in df_filtered.columns for col in ['tutor_id', 'tutor_name', 'check_in', 'check_out', 'shift_hours', 'snapshot_in', 'snapshot_out']):
        raw_df_copy = df_filtered.copy()
        if 'check_in' in raw_df_copy.columns: raw_df_copy['check_in_str'] = raw_df_copy['check_in'].dt.strftime('%Y-%m-%d %H:%M:%S')
        if 'check_out' in raw_df_copy.columns: raw_df_copy['check_out_str'] = raw_df_copy['check_out'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notna(x) else '—')
        if 'shift_hours' in raw_df_copy.columns: raw_df_copy['duration_str'] = raw_df_copy['shift_hours'].apply(lambda x: f"{x:.2f}" if pd.notna(x) and x > 0 else '—')
        cols_for_raw_context = ['tutor_id', 'tutor_name', 'check_in_str', 'check_out_str', 'duration_str', 'snapshot_in', 'snapshot_out']
        existing_cols_for_raw = [col for col in cols_for_raw_context if col in raw_df_copy.columns]
        if existing_cols_for_raw:
            raw_records_for_chart_context = raw_df_copy[existing_cols_for_raw].rename(columns={
                'check_in_str': 'check_in', 'check_out_str': 'check_out', 'duration_str': 'shift_hours'
            }).to_dict(orient='records')

    chart_key_requested = filters.get('chartKey', '')
    final_daily_checkins = daily_checkins_comparison if is_comparison_mode and chart_key_requested == 'daily_checkins' and daily_checkins_comparison else daily_checkins
    final_daily_hours = daily_hours_comparison if is_comparison_mode and chart_key_requested == 'daily_hours' and daily_hours_comparison else daily_hours

    # New datasets
    avg_session_duration_per_tutor = {}
    tutor_consistency_score = {}
    punctuality_analysis = {}
    session_duration_distribution = {}
    
    if 'tutor_name' in df_filtered.columns and 'shift_hours' in df_filtered.columns:
        # Average session duration per tutor
        avg_duration_stats = df_filtered.groupby('tutor_name')['shift_hours'].mean().round(2)
        avg_session_duration_per_tutor = avg_duration_stats.to_dict()
        
        # Tutor consistency score (lower standard deviation = higher consistency)
        consistency_stats = df_filtered.groupby('tutor_name')['shift_hours'].agg(['mean', 'std', 'count']).reset_index()
        consistency_stats = consistency_stats[consistency_stats['count'] >= 2]  # Only tutors with 2+ sessions
        
        for _, row in consistency_stats.iterrows():
            tutor_name = row['tutor_name']
            std_dev = row['std']
            mean_hours = row['mean']
            # Calculate consistency score (0-100, higher = more consistent)
            if mean_hours > 0 and not pd.isna(std_dev):
                cv = std_dev / mean_hours  # Coefficient of variation
                consistency_score = max(0, 100 - (cv * 100))  # Convert to 0-100 scale
                tutor_consistency_score[tutor_name] = round(consistency_score, 1)
    
    # Punctuality analysis (mock data for now - would need expected vs actual times)
    if 'check_in' in df_filtered.columns:
        # For now, create mock punctuality data based on hour of day
        # In a real implementation, this would compare expected vs actual check-in times
        punctuality_categories = {
            'Early (>15min early)': 0,
            'On Time (±15min)': 0,
            'Late (>15min late)': 0
        }
        
        # Mock punctuality calculation based on hour of day
        for _, row in df_filtered.iterrows():
            hour = row['check_in'].hour
            if hour < 9:  # Before 9 AM
                punctuality_categories['Early (>15min early)'] += 1
            elif 9 <= hour <= 17:  # 9 AM to 5 PM
                punctuality_categories['On Time (±15min)'] += 1
            else:  # After 5 PM
                punctuality_categories['Late (>15min late)'] += 1
        
        punctuality_analysis = punctuality_categories
    
    # Session duration distribution
    if 'shift_hours' in df_filtered.columns:
        duration_ranges = {
            'Short (<1h)': len(df_filtered[df_filtered['shift_hours'] < 1]),
            'Medium (1-3h)': len(df_filtered[(df_filtered['shift_hours'] >= 1) & (df_filtered['shift_hours'] < 3)]),
            'Long (3-6h)': len(df_filtered[(df_filtered['shift_hours'] >= 3) & (df_filtered['shift_hours'] < 6)]),
            'Extended (6h+)': len(df_filtered[df_filtered['shift_hours'] >= 6])
        }
        session_duration_distribution = duration_ranges

    # Helper function to ensure datasets are never completely empty
    def ensure_non_empty_dataset(data, dataset_name):
        """Ensure a dataset is not completely empty by providing placeholder data"""
        if not data or (isinstance(data, dict) and len(data) == 0) or (isinstance(data, list) and len(data) == 0):
            print(f"CHART_DATA - {dataset_name} is empty, providing placeholder")
            if dataset_name.endswith('_scatter'):
                return [{'label': 'No data available', 'x': 0, 'y': 0}]
            else:
                return {'No Data': 0}
        return data

    response_data = {
        'checkins_per_tutor': ensure_non_empty_dataset(checkins_per_tutor, 'checkins_per_tutor'),
        'hours_per_tutor': ensure_non_empty_dataset(hours_per_tutor, 'hours_per_tutor'),
        'daily_checkins': ensure_non_empty_dataset(final_daily_checkins, 'daily_checkins'),
        'daily_hours': ensure_non_empty_dataset(final_daily_hours, 'daily_hours'),
        'cumulative_checkins': ensure_non_empty_dataset(cumulative_checkins, 'cumulative_checkins'),
        'cumulative_hours': ensure_non_empty_dataset(cumulative_hours, 'cumulative_hours'),
        'hourly_checkins_dist': ensure_non_empty_dataset(hourly_checkins_dist, 'hourly_checkins_dist'),
        'monthly_hours': ensure_non_empty_dataset(monthly_hours, 'monthly_hours'),
        'avg_hours_per_day_of_week': ensure_non_empty_dataset(avg_hours_per_day_of_week, 'avg_hours_per_day_of_week'),
        'checkins_per_day_of_week': ensure_non_empty_dataset(checkins_per_day_of_week, 'checkins_per_day_of_week'),
        'hourly_activity_by_day': ensure_non_empty_dataset(hourly_activity_by_day, 'hourly_activity_by_day'),
        'forecast_daily_checkins': ensure_non_empty_dataset({}, 'forecast_daily_checkins'),
        'tutor_performance_scatter': ensure_non_empty_dataset(tutor_performance_scatter, 'tutor_performance_scatter'),
        'session_analysis_scatter': ensure_non_empty_dataset(session_analysis_scatter, 'session_analysis_scatter'),
        'session_efficiency_scatter': ensure_non_empty_dataset(session_efficiency_scatter, 'session_efficiency_scatter'),
        'raw_records_for_chart_context': ensure_non_empty_dataset(raw_records_for_chart_context, 'raw_records_for_chart_context'),
        'is_comparison_mode': is_comparison_mode,
        'avg_session_duration_per_tutor': ensure_non_empty_dataset(avg_session_duration_per_tutor, 'avg_session_duration_per_tutor'),
        'tutor_consistency_score': ensure_non_empty_dataset(tutor_consistency_score, 'tutor_consistency_score'),
        'punctuality_analysis': ensure_non_empty_dataset(punctuality_analysis, 'punctuality_analysis'),
        'session_duration_distribution': ensure_non_empty_dataset(session_duration_distribution, 'session_duration_distribution')
    }
    return jsonify(response_data)


@app.route('/check-in', methods=['POST'])
@role_required('manager')
def check_in():
    data = request.form
    tutor_id = data.get('tutor_id')
    tutor_name = data.get('tutor_name', f"Tutor {tutor_id}" if tutor_id else "Unknown Tutor")
    check_in_str = data.get('check_in')
    check_out_str = data.get('check_out')
    shift_hours_str = data.get('shift_hours')

    if not tutor_id or not check_in_str:
        return "Error: Tutor ID and Check-In time are required.", 400

    shift_hours_val = None # Initialize
    if shift_hours_str:
        try:
            shift_hours_val = float(shift_hours_str)
        except ValueError:
            shift_hours_val = None # Explicitly set to None if conversion fails

    # Only try to calculate if shift_hours_val is still None AND we have check_in and check_out times
    if shift_hours_val is None and check_in_str and check_out_str:
        try:
            ci = datetime.strptime(check_in_str, '%Y-%m-%dT%H:%M')
            co = datetime.strptime(check_out_str, '%Y-%m-%dT%H:%M')
            if co > ci: # This is the line your error was related to
                shift_hours_val = round(((co - ci).total_seconds()) / 3600, 2)
        except ValueError:
            # This 'pass' means if date parsing fails or co/ci are not comparable,
            # shift_hours_val will remain as it was (None or the value from shift_hours_str if it was valid)
            pass 

    new_entry = {
        "tutor_id": tutor_id, 
        "tutor_name": tutor_name,
        "check_in": check_in_str.replace('T', ' ') + ':00' if check_in_str else None,
        "check_out": check_out_str.replace('T', ' ') + ':00' if check_out_str else None,
        "shift_hours": shift_hours_val if shift_hours_val is not None else '', # Store empty string if None
        "snapshot_in": data.get('snapshot_in', ''), 
        "snapshot_out": data.get('snapshot_out', '') 
    }
    
    df_log = None # Initialize df_log
    try: 
        df_log = pd.read_csv(CSV_FILE)
    except FileNotFoundError: 
        # If file not found, create an empty DataFrame with the correct columns
        df_log = pd.DataFrame(columns=new_entry.keys()) 
        
    # Append the new entry
    df_log = pd.concat([df_log, pd.DataFrame([new_entry])], ignore_index=True)
    
    # Save back to CSV
    os.makedirs(os.path.dirname(CSV_FILE), exist_ok=True) # Ensure directory exists
    df_log.to_csv(CSV_FILE, index=False)
    
    return redirect(url_for('index'))

@app.route('/download-log')
@role_required('lead_tutor')
def download_log():
    try: return send_file(CSV_FILE, as_attachment=True, download_name='tutor_check_in_log.csv')
    except FileNotFoundError: return "Log file not found.", 404

@app.route('/export-chart-filtered-data', methods=['POST'])
@role_required('lead_tutor')
def export_chart_filtered_data():
    df_orig = load_data();
    if df_orig.empty: return "No data to export.", 404
    filters = request.json if request.is_json and request.json is not None else {}
    df_filtered = apply_filters(df_orig, filters)
    if df_filtered.empty: return "No data matches filters for export.", 404
    export_cols = ['tutor_id', 'tutor_name', 'check_in', 'check_out', 'shift_hours', 'snapshot_in', 'snapshot_out']
    for col in export_cols:
        if col not in df_filtered.columns: 
            df_filtered[col] = None
    df_export = df_filtered[export_cols].copy()
    if 'check_in' in df_export.columns: df_export['check_in'] = pd.to_datetime(df_export['check_in'], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
    if 'check_out' in df_export.columns: df_export['check_out'] = pd.to_datetime(df_export['check_out'], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
    df_export['check_in'] = df_export['check_in'].replace('NaT', '')
    df_export['check_out'] = df_export['check_out'].replace('NaT', '')
    output = io.BytesIO(); df_export.to_csv(output, index=False, encoding='utf-8'); output.seek(0)
    return send_file(output, as_attachment=True, download_name='chart_filtered_data.csv', mimetype='text/csv')

@app.route('/api/user-info')
@login_required
def api_user_info():
    """Get current user information from session"""
    try:
        current_user = get_current_user()
        if current_user:
            user_role = get_user_role(current_user.get('email', ''))
            return jsonify({
                'name': current_user.get('full_name', 'Unknown User'),
                'email': current_user.get('email', '<EMAIL>'),
                'role': user_role or 'tutor'
            })
        else:
            return jsonify({
                'name': 'Unknown User',
                'email': '<EMAIL>',
                'role': 'tutor'
            }), 401
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        return jsonify({
            'name': 'Error',
            'email': '<EMAIL>',
            'role': 'tutor'
        }), 500

if __name__ == '__main__':
    if not os.path.exists(os.path.dirname(CSV_FILE)): os.makedirs(os.path.dirname(CSV_FILE))
    if not os.path.exists(SNAPSHOTS_DIR): os.makedirs(SNAPSHOTS_DIR)
    app.run(debug=False)

